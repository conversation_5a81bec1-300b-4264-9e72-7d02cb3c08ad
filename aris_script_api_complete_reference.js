/**
 * ARIS Script API Complete Reference
 * 
 * This file provides comprehensive documentation and IntelliSense support for ARIS Scripting
 * Version: ********* and higher
 * 
 * @fileoverview Complete ARIS Script API reference with all objects, methods, and constants
 * <AUTHOR> from ARIS Documentation
 * @version *********
 * 
 * USAGE:
 * - Include this file in your VSCode workspace for IntelliSense support
 * - Use JSDoc comments for method signatures and parameter information
 * - All methods include parameter validation and return type information
 * 
 * BEST PRACTICES:
 * - Always use var to declare variables explicitly
 * - Use ArisData.Unique() for removing duplicates from ARIS object arrays
 * - Call database.clearCaches() when working with large datasets
 * - Use try/catch blocks for error handling
 * - Release unused variables by setting them to null
 * - Use SAVE_ONDEMAND for batch operations, then SAVE_NOW
 */

"use strict";

// ============================================================================
// GLOBAL DECLARATIONS FOR VSCODE INTELLISENSE
// ============================================================================

/**
 * @global
 * @type {ArisDataObject}
 */
var ArisData;

/**
 * @global
 * @type {ContextObject}
 */
var Context;

/**
 * @global
 * @type {DialogsObject}
 */
var Dialogs;

/**
 * @global
 * @type {ConstantsObject}
 */
var Constants;


// ============================================================================
// CONSTANTS OBJECT - ARIS Script Constants
// ============================================================================

/**
 * ARIS Constants Object containing all constant values used in ARIS scripting
 * @typedef {Object} ConstantsObject
 */
var Constants = {

    // ========================================================================
    // SEARCH CONSTANTS
    // ========================================================================

    /** @type {number} Search for object definitions */
    SEARCH_OBJDEF: 1,

    /** @type {number} Search for models */
    SEARCH_MODEL: 2,

    /** @type {number} Search for object occurrences */
    SEARCH_OBJOCC: 3,

    /** @type {number} Search for connections */
    SEARCH_CXN: 4,

    /** @type {number} Search comparison - equal */
    SEARCH_CMP_EQUAL: 1,

    /** @type {number} Search comparison - not equal */
    SEARCH_CMP_NOT_EQUAL: 2,

    /** @type {number} Search comparison - contains */
    SEARCH_CMP_CONTAINS: 3,

    /** @type {number} Search comparison - starts with */
    SEARCH_CMP_STARTS_WITH: 4,

    /** @type {number} Search comparison - ends with */
    SEARCH_CMP_ENDS_WITH: 5,

    // ========================================================================
    // OBJECT TYPE CONSTANTS
    // ========================================================================

    /** @type {number} Function object type */
    OT_FUNC: 1,

    /** @type {number} Event object type */
    OT_EVT: 2,

    /** @type {number} Organization unit object type */
    OT_ORG_UNIT: 3,

    /** @type {number} Position object type */
    OT_POS: 4,

    /** @type {number} Application system type object type */
    OT_APPL_SYS_TYPE: 5,

    /** @type {number} Action object type */
    OT_ACTION: 6,

    /** @type {number} Process support unit object type */
    OT_PROCESS_SUPPORT_UNIT: 7,

    /** @type {number} Role object type */
    OT_ROLE: 8,

    /** @type {number} Person type object type */
    OT_PERS_TYPE: 9,

    /** @type {number} Data cluster object type */
    OT_CLST: 10,

    /** @type {number} Entity type object type */
    OT_ENT_TYPE: 11,

    /** @type {number} Technical term object type */
    OT_TECH_TRM: 12,

    // ========================================================================
    // MODEL TYPE CONSTANTS
    // ========================================================================

    /** @type {number} EPC (Event-driven Process Chain) */
    MT_EEPC: 1,

    /** @type {number} EPC (column display) */
    MT_EEPC_COLUMN: 2,

    /** @type {number} EPC (row display) */
    MT_EEPC_ROW: 3,

    /** @type {number} Organization chart */
    MT_ORG_CHRT: 4,

    /** @type {number} Function allocation diagram */
    MT_FUNC_ALLOC_DGM: 5,

    /** @type {number} Application system type diagram */
    MT_APPL_SYS_TYPE_DGM: 6,

    /** @type {number} Process landscape */
    MT_PRCS_LST: 7,

    /** @type {number} Value added chain diagram */
    MT_VAL_ADD_CHN_DGM: 8,

    /** @type {number} Business controls diagram */
    MT_BUS_CNTRLS_DGM: 9,

    /** @type {number} Data model */
    MT_ERD: 10,

    /** @type {number} Knowledge structure diagram */
    MT_KNW_STR_DGM: 11,

    // ========================================================================
    // SYMBOL TYPE CONSTANTS
    // ========================================================================

    /** @type {number} Function symbol */
    ST_FUNC: 1,

    /** @type {number} Event symbol */
    ST_EVT: 2,

    /** @type {number} Process interface symbol */
    ST_PRCS_IF: 3,

    /** @type {number} Organization unit symbol */
    ST_ORG_UNIT: 4,

    /** @type {number} Position symbol */
    ST_POS: 5,

    /** @type {number} Role symbol */
    ST_ROLE: 6,

    /** @type {number} Person type symbol */
    ST_PERS_TYPE: 7,

    /** @type {number} Application system type symbol */
    ST_APPL_SYS_TYPE: 8,

    /** @type {number} Data cluster symbol */
    ST_CLST: 9,

    /** @type {number} Entity type symbol */
    ST_ENT_TYPE: 10,

    // ========================================================================
    // CONNECTION TYPE CONSTANTS
    // ========================================================================

    /** @type {number} Connection type - carries out */
    CT_CARRIES_OUT: 1,

    /** @type {number} Connection type - is responsible for */
    CT_IS_RESP_FOR: 2,

    /** @type {number} Connection type - decides on */
    CT_DECIDES_ON: 3,

    /** @type {number} Connection type - must be informed on */
    CT_MUST_BE_INF_ON: 4,

    /** @type {number} Connection type - can be consulted on */
    CT_CAN_BE_CONS_ON: 5,

    /** @type {number} Connection type - provides input for */
    CT_PROV_INP_FOR: 6,

    /** @type {number} Connection type - has output to */
    CT_HAS_OUT_TO: 7,

    /** @type {number} Connection type - uses */
    CT_USE_1: 8,

    /** @type {number} Connection type - supports */
    CT_SUPP_1: 9,

    /** @type {number} Connection type - is predecessor of */
    CT_IS_PRED_OF: 10,

    /** @type {number} Connection type - is successor of */
    CT_IS_SUCC_OF: 11,

    // ========================================================================
    // ATTRIBUTE TYPE CONSTANTS
    // ========================================================================

    /** @type {number} Name attribute */
    AT_NAME: 1,

    /** @type {number} Description attribute */
    AT_DESC: 2,

    /** @type {number} ID attribute */
    AT_ID: 3,

    /** @type {number} Type attribute */
    AT_TYPE: 4,

    /** @type {number} Creation time stamp */
    AT_CREAT_TIME_STMP: 5,

    /** @type {number} Last change time stamp */
    AT_LAST_CHG_TIME_STMP: 6,

    /** @type {number} Creator attribute */
    AT_CREATOR: 7,

    /** @type {number} Last user attribute */
    AT_LAST_USER: 8,

    /** @type {number} Responsible person attribute */
    AT_RESP_PERS: 9,

    /** @type {number} Status attribute */
    AT_STATUS: 10,

    /** @type {number} Version attribute */
    AT_VERSION: 11,

    /** @type {number} Comment attribute */
    AT_REM: 12,

    /** @type {number} Keywords attribute */
    AT_KEYW: 13,

    /** @type {number} Author attribute */
    AT_AUTH: 14,

    /** @type {number} Department attribute */
    AT_DEPT: 15,

    // ========================================================================
    // LANGUAGE/LOCALE CONSTANTS
    // ========================================================================

    /** @type {number} German language ID */
    LCID_GERMAN: 1031,

    /** @type {number} English (US) language ID */
    LCID_ENGLISH_US: 1033,

    /** @type {number} French language ID */
    LCID_FRENCH: 1036,

    /** @type {number} Spanish language ID */
    LCID_SPANISH: 1034,

    /** @type {number} Italian language ID */
    LCID_ITALIAN: 1040,

    /** @type {number} Portuguese language ID */
    LCID_PORTUGUESE: 2070,

    /** @type {number} Dutch language ID */
    LCID_DUTCH: 1043,

    /** @type {number} Japanese language ID */
    LCID_JAPANESE: 1041,

    // ========================================================================
    // CONNECTION DIRECTION CONSTANTS
    // ========================================================================

    /** @type {number} Incoming connections only */
    EDGES_IN: 1,

    /** @type {number} Outgoing connections only */
    EDGES_OUT: 2,

    /** @type {number} Both incoming and outgoing connections */
    EDGES_INOUT: 3,

    /** @type {number} All connections (bidirectional) */
    EDGES_ALL: 4,

    // ========================================================================
    // SAVE OPERATION CONSTANTS
    // ========================================================================

    /** @type {number} Auto save mode (default) */
    SAVE_AUTO: 1,

    /** @type {number} Save immediately */
    SAVE_IMMEDIATELY: 2,

    /** @type {number} Save on demand only */
    SAVE_ONDEMAND: 3,

    /** @type {number} Execute save now */
    SAVE_NOW: 4,

    // ========================================================================
    // FORMATTING CONSTANTS
    // ========================================================================

    /** @type {number} Bold text formatting */
    FMT_BOLD: 1,

    /** @type {number} Italic text formatting */
    FMT_ITALIC: 2,

    /** @type {number} Underline text formatting */
    FMT_UNDERLINE: 4,

    /** @type {number} Left alignment */
    FMT_LEFT: 8,

    /** @type {number} Center alignment */
    FMT_CENTER: 16,

    /** @type {number} Right alignment */
    FMT_RIGHT: 32,

    /** @type {number} Top vertical alignment */
    FMT_VTOP: 64,

    /** @type {number} Middle vertical alignment */
    FMT_VCENTER: 128,

    /** @type {number} Bottom vertical alignment */
    FMT_VBOTTOM: 256,

    /** @type {number} Repeat header formatting */
    FMT_REPEAT_HEADER: 512,

    // ========================================================================
    // COLOR CONSTANTS
    // ========================================================================

    /** @type {number} Black color */
    C_BLACK: 0x000000,

    /** @type {number} White color */
    C_WHITE: 0xFFFFFF,

    /** @type {number} Red color */
    C_RED: 0xFF0000,

    /** @type {number} Green color */
    C_GREEN: 0x00FF00,

    /** @type {number} Blue color */
    C_BLUE: 0x0000FF,

    /** @type {number} Yellow color */
    C_YELLOW: 0xFFFF00,

    /** @type {number} Transparent color */
    C_TRANSPARENT: 0x1000000,

    // ========================================================================
    // ERROR CONSTANTS
    // ========================================================================

    /** @type {number} No error */
    ERR_NOERROR: 0,

    /** @type {number} Cancel error */
    ERR_CANCEL: 1,

    /** @type {number} General error */
    ERR_GENERAL: 2,

    /** @type {number} Access denied error */
    ERR_ACCESS_DENIED: 3,

    /** @type {number} Invalid parameter error */
    ERR_INVALID_PARAM: 4,

    // ========================================================================
    // LOCATION CONSTANTS
    // ========================================================================

    /** @type {number} Script location */
    LOCATION_SCRIPT: 1,

    /** @type {number} Output location */
    LOCATION_OUTPUT: 2,

    /** @type {number} Common files location */
    LOCATION_COMMON_FILES: 3
};


// ============================================================================
// ARISDATA GLOBAL OBJECT
// ============================================================================

/**
 * ArisData Global Object - Main entry point for ARIS Script operations
 * @typedef {Object} ArisDataObject
 * @global
 */
var ArisData = {

    /**
     * Gets the currently active database
     * @returns {Database} The active database object
     * @example
     * var activeDB = ArisData.getActiveDatabase();
     * var allModels = activeDB.Find(Constants.SEARCH_MODEL);
     */
    getActiveDatabase: function() {},

    /**
     * Gets the selected databases from the context
     * @returns {Database[]} Array of selected database objects
     * @example
     * var selectedDatabases = ArisData.getSelectedDatabases();
     * for (var i = 0; i < selectedDatabases.length; i++) {
     *     var db = selectedDatabases[i];
     *     // Process each database
     * }
     */
    getSelectedDatabases: function() {},

    /**
     * Gets the selected groups from the current context
     * @returns {Group[]} Array of selected group objects
     * @example
     * var selectedGroups = ArisData.getSelectedGroups();
     * for (var i = 0; i < selectedGroups.length; i++) {
     *     var group = selectedGroups[i];
     *     var models = group.ModelList();
     * }
     */
    getSelectedGroups: function() {},

    /**
     * Gets the selected models from the current context
     * @returns {Model[]} Array of selected model objects
     * @example
     * var selectedModels = ArisData.getSelectedModels();
     * for (var i = 0; i < selectedModels.length; i++) {
     *     var model = selectedModels[i];
     *     var objOccs = model.ObjOccList();
     * }
     */
    getSelectedModels: function() {},

    /**
     * Gets the selected object definitions from the current context
     * @returns {ObjDef[]} Array of selected object definition objects
     * @example
     * var selectedObjDefs = ArisData.getSelectedObjDefs();
     * for (var i = 0; i < selectedObjDefs.length; i++) {
     *     var objDef = selectedObjDefs[i];
     *     var name = objDef.Name(Context.getSelectedLanguage());
     * }
     */
    getSelectedObjDefs: function() {},

    /**
     * Gets the selected object occurrences from the current context
     * @returns {ObjOcc[]} Array of selected object occurrence objects
     * @example
     * var selectedObjOccs = ArisData.getSelectedObjOccs();
     * for (var i = 0; i < selectedObjOccs.length; i++) {
     *     var objOcc = selectedObjOccs[i];
     *     var symbolType = objOcc.SymbolNum();
     * }
     */
    getSelectedObjOccs: function() {},

    /**
     * Gets the selected connections from the current context
     * @returns {CxnDef[]} Array of selected connection objects
     * @example
     * var selectedCxns = ArisData.getSelectedCxns();
     * for (var i = 0; i < selectedCxns.length; i++) {
     *     var cxn = selectedCxns[i];
     *     var sourceObj = cxn.SourceObjDef();
     *     var targetObj = cxn.TargetObjDef();
     * }
     */
    getSelectedCxns: function() {},

    /**
     * Removes duplicate objects from an array of ARIS objects
     * BEST PRACTICE: Use this instead of implementing your own duplicate removal
     * @param {Object[]} aObjects - Array of ARIS objects to make unique
     * @returns {Object[]} Array with duplicates removed
     * @example
     * var models = group1.ModelList().concat(group2.ModelList());
     * var uniqueModels = ArisData.Unique(models);
     */
    Unique: function(aObjects) {},

    /**
     * Sorts an array of ARIS objects by specified attribute
     * @param {Object[]} aObjects - Array of ARIS objects to sort
     * @param {number} nAttributeTypeNum - Attribute type number for sorting
     * @param {number} nLocale - Language ID for attribute values
     * @param {boolean} bDescending - Sort order (optional, default false)
     * @returns {Object[]} Sorted array of objects
     * @example
     * var sortedFunctions = ArisData.sort(funcList, Constants.AT_NAME, 
     *     Context.getSelectedLanguage(), false);
     */
    sort: function(aObjects, nAttributeTypeNum, nLocale, bDescending) {},

    /**
     * Opens a database connection with specified parameters
     * IMPORTANT: Always close the database when finished using database.close()
     * @param {string} sServerName - Server name
     * @param {string} sDatabaseName - Database name
     * @param {string} sUserName - User name
     * @param {string} sPassword - Password
     * @param {string} sFilterName - Filter name (optional)
     * @returns {Database} Opened database object or null if failed
     * @example
     * var db = ArisData.openDatabase("server", "database", "user", "pass", "filter");
     * if (db != null) {
     *     // Use database
     *     db.close(); // Always close when finished!
     * }
     */
    openDatabase: function(sServerName, sDatabaseName, sUserName, sPassword, sFilterName) {},

    /**
     * Manages save operations for ARIS objects
     * @param {number} nSaveMode - Save mode constant (SAVE_AUTO, SAVE_IMMEDIATELY, SAVE_ONDEMAND, SAVE_NOW)
     * @example
     * // For batch operations - more efficient
     * ArisData.Save(Constants.SAVE_ONDEMAND);
     * // ... perform many changes ...
     * ArisData.Save(Constants.SAVE_NOW);
     * 
     * // For immediate saving (default behavior)
     * ArisData.Save(Constants.SAVE_IMMEDIATELY);
     */
    Save: function(nSaveMode) {},

    /**
     * Gets the currently active filter
     * @returns {Filter} Active filter object
     * @example
     * var activeFilter = ArisData.ActiveFilter();
     * var customAttrTypeNum = activeFilter.UserDefinedAttributeTypeNum("GUID-HERE");
     */
    ActiveFilter: function() {},

    /**
     * Creates a new search item for advanced searching
     * @returns {ISearchItem} Search item object for building complex queries
     * @example
     * var db = ArisData.getActiveDatabase();
     * var searchItem = db.createSearchItem(Constants.AT_NAME, 
     *     Context.getSelectedLanguage(), "Process*", 
     *     Constants.SEARCH_CMP_EQUAL, false, true);
     */
    createSearchItem: function() {}
};


// ============================================================================
// DATABASE CLASS
// ============================================================================

/**
 * Database class representing an ARIS database
 * @class
 */
function Database() {}

/**
 * Performs a search in the database
 * @param {number} nSearchFlag - Search type constant (SEARCH_OBJDEF, SEARCH_MODEL, etc.)
 * @param {...number} nTypeNums - Object/model type numbers to search for (optional)
 * @returns {Object[]} Array of found objects
 * @example
 * var db = ArisData.getActiveDatabase();
 * var allModels = db.Find(Constants.SEARCH_MODEL);
 * var epcModels = db.Find(Constants.SEARCH_MODEL, Constants.MT_EEPC);
 * var functions = db.Find(Constants.SEARCH_OBJDEF, Constants.OT_FUNC);
 */
Database.prototype.Find = function(nSearchFlag, nTypeNums) {};

/**
 * Clears all cached objects from memory
 * IMPORTANT: Use when working with large datasets to free memory
 * WARNING: Ensure all objects are saved before clearing cache
 * @example
 * var db = ArisData.getActiveDatabase();
 * // ... work with large amounts of data ...
 * db.clearCaches(); // Free memory
 */
Database.prototype.clearCaches = function() {};

/**
 * Gets the database name
 * @param {number} nLocale - Language ID
 * @returns {string} Database name in specified language
 * @example
 * var dbName = database.Name(Context.getSelectedLanguage());
 */
Database.prototype.Name = function(nLocale) {};

/**
 * Gets the root group of the database
 * @returns {Group} Root group object
 * @example
 * var rootGroup = database.RootGroup();
 * var allSubGroups = rootGroup.Childs(true);
 */
Database.prototype.RootGroup = function() {};

/**
 * Gets all groups in the database
 * @param {boolean} bRecursive - Include subgroups recursively (optional, default true)
 * @returns {Group[]} Array of group objects
 * @example
 * var allGroups = database.GroupList(true);
 * for (var i = 0; i < allGroups.length; i++) {
 *     var group = allGroups[i];
 *     var models = group.ModelList();
 * }
 */
Database.prototype.GroupList = function(bRecursive) {};

/**
 * Creates a search item for advanced searching
 * @param {number} nAttributeTypeNum - Attribute type number
 * @param {number} nLocale - Language ID  
 * @param {string} sSearchValue - Value to search for
 * @param {number} nComparisonOperator - Comparison operator constant
 * @param {boolean} bCaseSensitive - Case sensitive search
 * @param {boolean} bUseWildcards - Use wildcards in search
 * @returns {ISearchItem} Search item object
 * @example
 * var searchItem = database.createSearchItem(
 *     Constants.AT_NAME, Context.getSelectedLanguage(), 
 *     "Process*", Constants.SEARCH_CMP_EQUAL, false, true);
 */
Database.prototype.createSearchItem = function(nAttributeTypeNum, nLocale, sSearchValue, nComparisonOperator, bCaseSensitive, bUseWildcards) {};

/**
 * Closes the database connection
 * IMPORTANT: Always close databases opened with ArisData.openDatabase()
 * @example
 * var db = ArisData.openDatabase("server", "db", "user", "pass");
 * if (db != null) {
 *     // Use database
 *     db.close(); // Always close!
 * }
 */
Database.prototype.close = function() {};

/**
 * Gets the ArisData object for this database
 * Used for managing save operations on opened databases
 * @returns {ArisDataObject} ArisData object for this database
 * @example
 * var db = ArisData.openDatabase("server", "db", "user", "pass");
 * db.getArisData().Save(Constants.SAVE_ONDEMAND);
 */
Database.prototype.getArisData = function() {};

/**
 * Checks if the database is valid and accessible
 * @returns {boolean} True if database is valid
 * @example
 * if (database.IsValid()) {
 *     // Safe to use database
 * }
 */
Database.prototype.IsValid = function() {};

/**
 * Gets database server name
 * @returns {string} Server name
 * @example
 * var serverName = database.ServerName();
 */
Database.prototype.ServerName = function() {};

/**
 * Gets active language of the database
 * @returns {number} Language ID
 * @example
 * var dbLanguage = database.ActiveLanguage();
 */
Database.prototype.ActiveLanguage = function() {};

/**
 * Gets all supported languages in the database
 * @returns {number[]} Array of language IDs
 * @example
 * var languages = database.LanguageList();
 */
Database.prototype.LanguageList = function() {};

/**
 * Gets database GUID
 * @returns {string} Unique identifier of the database
 * @example
 * var dbGuid = database.GUID();
 */
Database.prototype.GUID = function() {};


// ============================================================================
// GROUP CLASS
// ============================================================================

/**
 * Group class representing an ARIS group/folder
 * @class
 */
function Group() {}

/**
 * Gets the name of the group
 * @param {number} nLocale - Language ID
 * @returns {string} Group name in specified language
 * @example
 * var groupName = group.Name(Context.getSelectedLanguage());
 */
Group.prototype.Name = function(nLocale) {};

/**
 * Gets all models in this group and optionally subgroups
 * PERFORMANCE TIP: Use with type filter for better performance
 * @param {boolean} bRecursive - Include subgroups (default true)
 * @param {number[]} aModelTypeNums - Array of model type numbers to filter (optional)
 * @returns {Model[]} Array of model objects
 * @example
 * // Get all models recursively
 * var allModels = group.ModelList(true);
 * 
 * // Get only EPC models
 * var epcModels = group.ModelList(true, [Constants.MT_EEPC]);
 * 
 * // More efficient than getting all and filtering manually
 * var specificModels = group.ModelList(true, [Constants.MT_EEPC, Constants.MT_ORG_CHRT]);
 */
Group.prototype.ModelList = function(bRecursive, aModelTypeNums) {};

/**
 * Gets models with advanced search criteria
 * @param {boolean} bRecursive - Include subgroups
 * @param {number[]} aModelTypeNums - Model type numbers
 * @param {ISearchItem} searchSpec - Search specification
 * @returns {Model[]} Filtered array of model objects
 * @example
 * var searchItem = database.createSearchItem(Constants.AT_NAME, 
 *     Context.getSelectedLanguage(), "Process*", Constants.SEARCH_CMP_EQUAL, false, true);
 * var models = group.ModelList(true, [Constants.MT_EEPC], searchItem);
 */
Group.prototype.ModelListWithSearch = function(bRecursive, aModelTypeNums, searchSpec) {};

/**
 * Gets all object definitions in this group and optionally subgroups
 * @param {boolean} bRecursive - Include subgroups (default true)
 * @param {number[]} aObjectTypeNums - Array of object type numbers to filter (optional)
 * @returns {ObjDef[]} Array of object definition objects
 * @example
 * // Get all object definitions
 * var allObjDefs = group.ObjDefList(true);
 * 
 * // Get only functions and events
 * var funcAndEvents = group.ObjDefList(true, [Constants.OT_FUNC, Constants.OT_EVT]);
 */
Group.prototype.ObjDefList = function(bRecursive, aObjectTypeNums) {};

/**
 * Gets object definitions with advanced search criteria
 * @param {boolean} bRecursive - Include subgroups
 * @param {number[]} aObjectTypeNums - Object type numbers
 * @param {ISearchItem} searchSpec - Search specification
 * @returns {ObjDef[]} Filtered array of object definition objects
 * @example
 * var searchItem = database.createSearchItem(Constants.AT_NAME, 
 *     Context.getSelectedLanguage(), "Customer*", Constants.SEARCH_CMP_EQUAL, false, true);
 * var objDefs = group.ObjDefList(true, [Constants.OT_FUNC], searchItem);
 */
Group.prototype.ObjDefListWithSearch = function(bRecursive, aObjectTypeNums, searchSpec) {};

/**
 * Gets child groups (subgroups)
 * @param {boolean} bRecursive - Get all descendants (default false)
 * @returns {Group[]} Array of child group objects
 * @example
 * // Get immediate child groups
 * var childGroups = group.Childs();
 * 
 * // Get all descendant groups
 * var allSubGroups = group.Childs(true);
 */
Group.prototype.Childs = function(bRecursive) {};

/**
 * Gets the parent group
 * @returns {Group} Parent group object, null if root group
 * @example
 * var parentGroup = group.Parent();
 * if (parentGroup != null) {
 *     var parentName = parentGroup.Name(Context.getSelectedLanguage());
 * }
 */
Group.prototype.Parent = function() {};

/**
 * Gets the database that contains this group
 * @returns {Database} Database object
 * @example
 * var database = group.Database();
 * var dbName = database.Name(Context.getSelectedLanguage());
 */
Group.prototype.Database = function() {};

/**
 * Gets the full path of the group
 * @param {number} nLocale - Language ID
 * @returns {string} Full path from root to this group
 * @example
 * var fullPath = group.Path(Context.getSelectedLanguage());
 * // Returns something like "Main Group\Processes\Core Processes"
 */
Group.prototype.Path = function(nLocale) {};

/**
 * Checks if this group is equal to another group
 * @param {Group} otherGroup - Group to compare with
 * @returns {boolean} True if groups are equal
 * @example
 * if (group1.IsEqual(group2)) {
 *     // Same group
 * }
 */
Group.prototype.IsEqual = function(otherGroup) {};

/**
 * Gets the GUID of the group
 * @returns {string} Unique identifier
 * @example
 * var groupGuid = group.GUID();
 */
Group.prototype.GUID = function() {};

/**
 * Checks if the group is valid
 * @returns {boolean} True if group is valid and accessible
 * @example
 * if (group.IsValid()) {
 *     var models = group.ModelList();
 * }
 */
Group.prototype.IsValid = function() {};

/**
 * Creates a new subgroup in this group
 * @param {string} sName - Name of the new group
 * @param {number} nLocale - Language ID
 * @returns {Group} Created group object
 * @example
 * var newGroup = parentGroup.CreateChildGroup("New Processes", Context.getSelectedLanguage());
 */
Group.prototype.CreateChildGroup = function(sName, nLocale) {};

/**
 * Creates a new model in this group
 * @param {number} nModelType - Model type number
 * @param {string} sName - Model name
 * @param {number} nLocale - Language ID
 * @returns {Model} Created model object
 * @example
 * var newModel = group.CreateModel(Constants.MT_EEPC, "New Process", Context.getSelectedLanguage());
 */
Group.prototype.CreateModel = function(nModelType, sName, nLocale) {};

/**
 * Gets access rights for this group
 * @returns {Object} Access rights object
 * @example
 * var accessRights = group.AccessRights();
 */
Group.prototype.AccessRights = function() {};


// ============================================================================
// MODEL CLASS
// ============================================================================

/**
 * Model class representing an ARIS model
 * @class
 */
function Model() {}

/**
 * Gets the name of the model
 * @param {number} nLocale - Language ID
 * @returns {string} Model name in specified language
 * @example
 * var modelName = model.Name(Context.getSelectedLanguage());
 */
Model.prototype.Name = function(nLocale) {};

/**
 * Gets all object occurrences in the model
 * NOTE: Reading connections also loads connected object definitions into memory
 * @returns {ObjOcc[]} Array of object occurrence objects
 * @example
 * var objOccs = model.ObjOccList();
 * for (var i = 0; i < objOccs.length; i++) {
 *     var occ = objOccs[i];
 *     var objDef = occ.ObjDef();
 *     // Process each occurrence
 * }
 */
Model.prototype.ObjOccList = function() {};

/**
 * Gets object occurrences filtered by object type
 * @param {number} nObjType - Object type number (-1 for all types)
 * @returns {ObjOcc[]} Array of filtered object occurrences
 * @example
 * var functions = model.ObjOccListFilter(Constants.OT_FUNC);
 * var events = model.ObjOccListFilter(Constants.OT_EVT);
 */
Model.prototype.ObjOccListFilter = function(nObjType) {};

/**
 * Gets object occurrences by symbol type (PERFORMANCE OPTIMIZED)
 * BEST PRACTICE: Use this instead of filtering manually for better performance
 * @param {number[]} aSymbolTypeNums - Array of symbol type numbers
 * @returns {ObjOcc[]} Array of object occurrences with specified symbols
 * @example
 * // Instead of manual filtering - SLOW:
 * // var allOccs = model.ObjOccList();
 * // Filter manually...
 * 
 * // Use this method - FAST:
 * var funcOccs = model.ObjOccListBySymbol([Constants.ST_FUNC, Constants.ST_PRCS_IF]);
 */
Model.prototype.ObjOccListBySymbol = function(aSymbolTypeNums) {};

/**
 * Gets all object definitions used in the model
 * @returns {ObjDef[]} Array of object definition objects
 * @example
 * var objDefs = model.ObjDefList();
 * var uniqueObjDefs = ArisData.Unique(objDefs); // Remove duplicates if needed
 */
Model.prototype.ObjDefList = function() {};

/**
 * Gets object definitions by type (PERFORMANCE OPTIMIZED)
 * BEST PRACTICE: Use this instead of filtering ObjDefList manually
 * @param {number[]} aObjTypeNums - Array of object type numbers
 * @returns {ObjDef[]} Array of object definitions with specified types
 * @example
 * // Efficient way to get functions and events
 * var funcAndEvents = model.ObjDefListByTypes([Constants.OT_FUNC, Constants.OT_EVT]);
 */
Model.prototype.ObjDefListByTypes = function(aObjTypeNums) {};

/**
 * Gets object definitions filtered by type
 * @param {number} nObjType - Object type number
 * @returns {ObjDef[]} Array of filtered object definitions
 * @example
 * var functions = model.ObjDefListFilter(Constants.OT_FUNC);
 */
Model.prototype.ObjDefListFilter = function(nObjType) {};

/**
 * Gets all connections in the model
 * @returns {CxnOcc[]} Array of connection occurrence objects
 * @example
 * var connections = model.CxnOccList();
 * for (var i = 0; i < connections.length; i++) {
 *     var cxn = connections[i];
 *     var sourceOcc = cxn.SourceObjOcc();
 *     var targetOcc = cxn.TargetObjOcc();
 * }
 */
Model.prototype.CxnOccList = function() {};

/**
 * Gets connections filtered by type
 * @param {number} nCxnType - Connection type number (-1 for all)
 * @returns {CxnOcc[]} Array of filtered connection occurrences
 * @example
 * var carryOutCxns = model.CxnOccListFilter(Constants.CT_CARRIES_OUT);
 */
Model.prototype.CxnOccListFilter = function(nCxnType) {};

/**
 * Gets the group that contains this model
 * @returns {Group} Parent group object
 * @example
 * var parentGroup = model.Group();
 * var groupName = parentGroup.Name(Context.getSelectedLanguage());
 */
Model.prototype.Group = function() {};

/**
 * Gets the database that contains this model
 * @returns {Database} Database object
 * @example
 * var database = model.Database();
 */
Model.prototype.Database = function() {};

/**
 * Gets model type number
 * @returns {number} Model type number
 * @example
 * var modelType = model.TypeNum();
 * if (modelType == Constants.MT_EEPC) {
 *     // This is an EPC model
 * }
 */
Model.prototype.TypeNum = function() {};

/**
 * Gets model GUID
 * @returns {string} Unique identifier
 * @example
 * var modelGuid = model.GUID();
 */
Model.prototype.GUID = function() {};

/**
 * Checks if the model is valid
 * @returns {boolean} True if model is valid
 * @example
 * if (model.IsValid()) {
 *     var objOccs = model.ObjOccList();
 * }
 */
Model.prototype.IsValid = function() {};

/**
 * Gets attribute value
 * NOTE: Reading one attribute loads ALL attributes into memory
 * @param {number} nAttrType - Attribute type number
 * @param {number} nLocale - Language ID
 * @returns {Attribute} Attribute object
 * @example
 * var nameAttr = model.Attribute(Constants.AT_NAME, Context.getSelectedLanguage());
 * var name = nameAttr.getValue();
 */
Model.prototype.Attribute = function(nAttrType, nLocale) {};

/**
 * Gets all attributes of the model
 * @param {number} nLocale - Language ID
 * @returns {Attribute[]} Array of attribute objects
 * @example
 * var allAttrs = model.AttributeList(Context.getSelectedLanguage());
 */
Model.prototype.AttributeList = function(nLocale) {};

/**
 * Sets attribute value
 * @param {number} nAttrType - Attribute type number
 * @param {number} nLocale - Language ID
 * @param {string} sValue - Attribute value
 * @returns {boolean} True if successful
 * @example
 * var success = model.Attribute(Constants.AT_DESC, Context.getSelectedLanguage(), "New description");
 */
Model.prototype.setAttribute = function(nAttrType, nLocale, sValue) {};

/**
 * Creates a new object occurrence in the model
 * @param {ObjDef} objDef - Object definition to place
 * @param {number} nSymbolType - Symbol type number
 * @param {number} nX - X coordinate
 * @param {number} nY - Y coordinate
 * @returns {ObjOcc} Created object occurrence
 * @example
 * var objOcc = model.CreateObjOcc(Constants.ST_FUNC, objDef, 100, 100);
 */
Model.prototype.CreateObjOcc = function(objDef, nSymbolType, nX, nY) {};

/**
 * Creates a connection between two object occurrences
 * @param {ObjOcc} sourceOcc - Source object occurrence
 * @param {ObjOcc} targetOcc - Target object occurrence  
 * @param {number} nCxnType - Connection type number
 * @returns {CxnOcc} Created connection occurrence
 * @example
 * var cxnOcc = model.CreateCxnOcc(sourceOcc, targetOcc, Constants.CT_CARRIES_OUT);
 */
Model.prototype.CreateCxnOcc = function(sourceOcc, targetOcc, nCxnType) {};

/**
 * Deletes the model
 * @returns {boolean} True if successful
 * @example
 * if (model.Delete()) {
 *     // Model deleted successfully
 * }
 */
Model.prototype.Delete = function() {};

/**
 * Copies the model to another group
 * @param {Group} targetGroup - Target group
 * @param {boolean} bWithAssignments - Copy assignments
 * @returns {Model} Copied model object
 * @example
 * var copiedModel = model.Copy(targetGroup, true);
 */
Model.prototype.Copy = function(targetGroup, bWithAssignments) {};

/**
 * Checks if this model equals another model
 * @param {Model} otherModel - Model to compare with
 * @returns {boolean} True if models are equal
 * @example
 * if (model1.IsEqual(model2)) {
 *     // Same model
 * }
 */
Model.prototype.IsEqual = function(otherModel) {};

/**
 * Gets model graphic as image
 * @param {boolean} bWithBackground - Include background
 * @param {boolean} bWithFrame - Include frame
 * @returns {Object} Image object
 * @example
 * Context.setProperty("model-as-emf", true); // Better quality
 * var modelImage = model.Graphic(false, true);
 */
Model.prototype.Graphic = function(bWithBackground, bWithFrame) {};


// ============================================================================
// OBJDEF CLASS - Object Definition
// ============================================================================

/**
 * ObjDef class representing an ARIS object definition
 * @class
 */
function ObjDef() {}

/**
 * Gets the name of the object definition
 * @param {number} nLocale - Language ID
 * @returns {string} Object name in specified language
 * @example
 * var objName = objDef.Name(Context.getSelectedLanguage());
 */
ObjDef.prototype.Name = function(nLocale) {};

/**
 * Gets object type number
 * @returns {number} Object type number
 * @example
 * var objType = objDef.TypeNum();
 * if (objType == Constants.OT_FUNC) {
 *     // This is a function
 * }
 */
ObjDef.prototype.TypeNum = function() {};

/**
 * Gets attribute value
 * NOTE: Reading one attribute loads ALL attributes into memory
 * @param {number} nAttrType - Attribute type number
 * @param {number} nLocale - Language ID
 * @returns {Attribute} Attribute object
 * @example
 * var descAttr = objDef.Attribute(Constants.AT_DESC, Context.getSelectedLanguage());
 * var description = descAttr.getValue();
 */
ObjDef.prototype.Attribute = function(nAttrType, nLocale) {};

/**
 * Gets all attributes of the object definition
 * @param {number} nLocale - Language ID
 * @returns {Attribute[]} Array of attribute objects
 * @example
 * var allAttrs = objDef.AttributeList(Context.getSelectedLanguage());
 */
ObjDef.prototype.AttributeList = function(nLocale) {};

/**
 * Sets attribute value
 * @param {number} nAttrType - Attribute type number
 * @param {number} nLocale - Language ID
 * @param {string} sValue - Attribute value
 * @returns {boolean} True if successful
 * @example
 * var success = objDef.Attribute(Constants.AT_DESC, Context.getSelectedLanguage(), "New description");
 */
ObjDef.prototype.setAttribute = function(nAttrType, nLocale, sValue) {};

/**
 * Gets all connections of this object definition
 * NOTE: Reading one connection loads ALL connections into memory
 * @param {number} nDirection - Connection direction (EDGES_IN, EDGES_OUT, EDGES_INOUT)
 * @returns {CxnDef[]} Array of connection definition objects
 * @example
 * var outgoingCxns = objDef.CxnList(Constants.EDGES_OUT);
 * var allCxns = objDef.CxnList(Constants.EDGES_INOUT);
 */
ObjDef.prototype.CxnList = function(nDirection) {};

/**
 * Gets connections filtered by type and direction
 * @param {number} nDirection - Connection direction
 * @param {number} nCxnType - Connection type number
 * @returns {CxnDef[]} Array of filtered connection definitions
 * @example
 * var carryOutCxns = objDef.CxnListFilter(Constants.EDGES_OUT, Constants.CT_CARRIES_OUT);
 */
ObjDef.prototype.CxnListFilter = function(nDirection, nCxnType) {};

/**
 * Gets models assigned to this object definition (PERFORMANCE OPTIMIZED)
 * BEST PRACTICE: Use this instead of iterating through all models
 * @param {number[]} aModelTypeNums - Array of model type numbers (optional)
 * @returns {Model[]} Array of assigned model objects
 * @example
 * // Get all assigned models
 * var assignedModels = objDef.AssignedModels();
 * 
 * // Get only EPC models assigned to this object
 * var epcModels = objDef.AssignedModels([Constants.MT_EEPC, Constants.MT_EEPC_COLUMN]);
 */
ObjDef.prototype.AssignedModels = function(aModelTypeNums) {};

/**
 * Gets all occurrences of this object definition in models
 * @returns {ObjOcc[]} Array of object occurrence objects
 * @example
 * var objOccs = objDef.OccList();
 * for (var i = 0; i < objOccs.length; i++) {
 *     var model = objOccs[i].Model();
 *     // Process each occurrence
 * }
 */
ObjDef.prototype.OccList = function() {};

/**
 * Gets the group that contains this object definition
 * @returns {Group} Parent group object
 * @example
 * var parentGroup = objDef.Group();
 */
ObjDef.prototype.Group = function() {};

/**
 * Gets the database that contains this object definition
 * @returns {Database} Database object
 * @example
 * var database = objDef.Database();
 */
ObjDef.prototype.Database = function() {};

/**
 * Gets object GUID
 * @returns {string} Unique identifier
 * @example
 * var objGuid = objDef.GUID();
 */
ObjDef.prototype.GUID = function() {};

/**
 * Checks if the object definition is valid
 * @returns {boolean} True if object is valid
 * @example
 * if (objDef.IsValid()) {
 *     var name = objDef.Name(Context.getSelectedLanguage());
 * }
 */
ObjDef.prototype.IsValid = function() {};

/**
 * Checks if this object definition equals another
 * @param {ObjDef} otherObjDef - Object definition to compare with
 * @returns {boolean} True if objects are equal
 * @example
 * if (objDef1.IsEqual(objDef2)) {
 *     // Same object definition
 * }
 */
ObjDef.prototype.IsEqual = function(otherObjDef) {};

/**
 * Creates an occurrence of this object in a model
 * @param {Model} model - Target model
 * @param {number} nSymbolType - Symbol type number
 * @param {number} nX - X coordinate
 * @param {number} nY - Y coordinate
 * @returns {ObjOcc} Created object occurrence
 * @example
 * var objOcc = objDef.CreateObjOcc(model, Constants.ST_FUNC, 100, 100);
 */
ObjDef.prototype.CreateObjOcc = function(model, nSymbolType, nX, nY) {};

/**
 * Deletes the object definition
 * WARNING: This also deletes all occurrences in models
 * @returns {boolean} True if successful
 * @example
 * if (objDef.Delete()) {
 *     // Object definition deleted
 * }
 */
ObjDef.prototype.Delete = function() {};

// ============================================================================
// OBJOCC CLASS - Object Occurrence
// ============================================================================

/**
 * ObjOcc class representing an object occurrence in a model
 * @class
 */
function ObjOcc() {}

/**
 * Gets the object definition for this occurrence
 * @returns {ObjDef} Object definition object
 * @example
 * var objDef = objOcc.ObjDef();
 * var objName = objDef.Name(Context.getSelectedLanguage());
 */
ObjOcc.prototype.ObjDef = function() {};

/**
 * Gets the model that contains this occurrence
 * @returns {Model} Model object
 * @example
 * var model = objOcc.Model();
 * var modelName = model.Name(Context.getSelectedLanguage());
 */
ObjOcc.prototype.Model = function() {};

/**
 * Gets the symbol type number
 * @returns {number} Symbol type number
 * @example
 * var symbolType = objOcc.SymbolNum();
 * if (symbolType == Constants.ST_FUNC) {
 *     // This is a function symbol
 * }
 */
ObjOcc.prototype.SymbolNum = function() {};

/**
 * Gets X coordinate of the occurrence
 * @returns {number} X position
 * @example
 * var x = objOcc.X();
 */
ObjOcc.prototype.X = function() {};

/**
 * Gets Y coordinate of the occurrence
 * @returns {number} Y position
 * @example
 * var y = objOcc.Y();
 */
ObjOcc.prototype.Y = function() {};

/**
 * Sets position of the occurrence
 * @param {number} nX - X coordinate
 * @param {number} nY - Y coordinate
 * @returns {boolean} True if successful
 * @example
 * var success = objOcc.setPosition(150, 200);
 */
ObjOcc.prototype.setPosition = function(nX, nY) {};

/**
 * Gets width of the occurrence
 * @returns {number} Width in pixels
 * @example
 * var width = objOcc.Width();
 */
ObjOcc.prototype.Width = function() {};

/**
 * Gets height of the occurrence
 * @returns {number} Height in pixels
 * @example
 * var height = objOcc.Height();
 */
ObjOcc.prototype.Height = function() {};

/**
 * Sets size of the occurrence
 * @param {number} nWidth - Width
 * @param {number} nHeight - Height
 * @returns {boolean} True if successful
 * @example
 * var success = objOcc.setSize(100, 50);
 */
ObjOcc.prototype.setSize = function(nWidth, nHeight) {};

/**
 * Gets incoming connection occurrences
 * @returns {CxnOcc[]} Array of incoming connection occurrences
 * @example
 * var incomingCxns = objOcc.InEdges();
 */
ObjOcc.prototype.InEdges = function() {};

/**
 * Gets outgoing connection occurrences
 * @returns {CxnOcc[]} Array of outgoing connection occurrences
 * @example
 * var outgoingCxns = objOcc.OutEdges();
 */
ObjOcc.prototype.OutEdges = function() {};

/**
 * Gets all connection occurrences (incoming and outgoing)
 * @returns {CxnOcc[]} Array of all connection occurrences
 * @example
 * var allCxns = objOcc.Edges();
 */
ObjOcc.prototype.Edges = function() {};

/**
 * Checks if this occurrence is valid
 * @returns {boolean} True if occurrence is valid
 * @example
 * if (objOcc.IsValid()) {
 *     var objDef = objOcc.ObjDef();
 * }
 */
ObjOcc.prototype.IsValid = function() {};

/**
 * Checks if this occurrence equals another
 * @param {ObjOcc} otherObjOcc - Occurrence to compare with
 * @returns {boolean} True if occurrences are equal
 * @example
 * if (objOcc1.IsEqual(objOcc2)) {
 *     // Same occurrence
 * }
 */
ObjOcc.prototype.IsEqual = function(otherObjOcc) {};

/**
 * Deletes the occurrence from the model
 * @returns {boolean} True if successful
 * @example
 * if (objOcc.Delete()) {
 *     // Occurrence deleted from model
 * }
 */
ObjOcc.prototype.Delete = function() {};

// ============================================================================
// CXNDEF CLASS - Connection Definition
// ============================================================================

/**
 * CxnDef class representing a connection definition between two object definitions
 * @class
 */
function CxnDef() {}

/**
 * Gets the source object definition
 * @returns {ObjDef} Source object definition
 * @example
 * var sourceObj = cxnDef.SourceObjDef();
 */
CxnDef.prototype.SourceObjDef = function() {};

/**
 * Gets the target object definition
 * @returns {ObjDef} Target object definition
 * @example
 * var targetObj = cxnDef.TargetObjDef();
 */
CxnDef.prototype.TargetObjDef = function() {};

/**
 * Gets connection type number
 * @returns {number} Connection type number
 * @example
 * var cxnType = cxnDef.TypeNum();
 */
CxnDef.prototype.TypeNum = function() {};

/**
 * Gets all occurrences of this connection
 * @returns {CxnOcc[]} Array of connection occurrences
 * @example
 * var cxnOccs = cxnDef.OccList();
 */
CxnDef.prototype.OccList = function() {};

// ============================================================================
// CXNOCC CLASS - Connection Occurrence
// ============================================================================

/**
 * CxnOcc class representing a connection occurrence in a model
 * @class
 */
function CxnOcc() {}

/**
 * Gets the connection definition for this occurrence
 * @returns {CxnDef} Connection definition object
 * @example
 * var cxnDef = cxnOcc.CxnDef();
 */
CxnOcc.prototype.CxnDef = function() {};

/**
 * Gets the source object occurrence
 * @returns {ObjOcc} Source object occurrence
 * @example
 * var sourceOcc = cxnOcc.SourceObjOcc();
 */
CxnOcc.prototype.SourceObjOcc = function() {};

/**
 * Gets the target object occurrence
 * @returns {ObjOcc} Target object occurrence
 * @example
 * var targetOcc = cxnOcc.TargetObjOcc();
 */
CxnOcc.prototype.TargetObjOcc = function() {};

/**
 * Gets the model containing this connection occurrence
 * @returns {Model} Model object
 * @example
 * var model = cxnOcc.Model();
 */
CxnOcc.prototype.Model = function() {};

/**
 * Deletes the connection occurrence
 * @returns {boolean} True if successful
 * @example
 * if (cxnOcc.Delete()) {
 *     // Connection occurrence deleted
 * }
 */
CxnOcc.prototype.Delete = function() {};


// ============================================================================
// CONTEXT OBJECT
// ============================================================================

/**
 * Context Object - Provides environment and configuration methods for ARIS scripts
 * @typedef {Object} ContextObject
 * @global
 */
var Context = {

    /**
     * Gets the currently selected language
     * @returns {number} Language ID (LCID)
     * @example
     * var currentLang = Context.getSelectedLanguage();
     * var objName = objDef.Name(currentLang);
     */
    getSelectedLanguage: function() {},

    /**
     * Creates an output object for report generation
     * @param {string} sOutputFormat - Output format (optional)
     * @returns {OutputObject} Output object for creating reports
     * @example
     * var oOutput = Context.createOutputObject();
     * oOutput.BeginTable(100, Constants.C_BLACK, Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);
     */
    createOutputObject: function(sOutputFormat) {},

    /**
     * Gets the selected file from file dialog
     * @returns {File} Selected file object
     * @example
     * var selectedFile = Context.getSelectedFile();
     */
    getSelectedFile: function() {},

    /**
     * Gets a file from the script location
     * @param {string} sFileName - File name
     * @param {number} nLocation - Location constant (LOCATION_SCRIPT, etc.)
     * @returns {File} File object
     * @example
     * var templateFile = Context.getFile("template.xlt", Constants.LOCATION_SCRIPT);
     */
    getFile: function(sFileName, nLocation) {},

    /**
     * Creates an Excel workbook
     * @param {string} sFileName - Output file name
     * @param {File} templateFile - Template file (optional)
     * @returns {Workbook} Excel workbook object
     * @example
     * var template = Context.getFile("template.xlt", Constants.LOCATION_SCRIPT);
     * var workbook = Context.createExcelWorkbook("output.xls", template);
     */
    createExcelWorkbook: function(sFileName, templateFile) {},

    /**
     * Sets a property for the script execution
     * @param {string} sPropertyName - Property name
     * @param {*} value - Property value
     * @example
     * // For better PDF quality of model graphics
     * Context.setProperty("model-as-emf", true);
     */
    setProperty: function(sPropertyName, value) {},

    /**
     * Gets a property value
     * @param {string} sPropertyName - Property name
     * @returns {*} Property value
     * @example
     * var emfEnabled = Context.getProperty("model-as-emf");
     */
    getProperty: function(sPropertyName) {},

    /**
     * Sets script error status
     * @param {number} nErrorCode - Error code constant
     * @example
     * // Cancel script execution on error
     * Context.setScriptError(Constants.ERR_CANCEL);
     */
    setScriptError: function(nErrorCode) {},

    /**
     * Gets the script error status
     * @returns {number} Error code
     * @example
     * var errorStatus = Context.getScriptError();
     */
    getScriptError: function() {},

    /**
     * Writes output to the console/log
     * @param {string} sMessage - Message to write
     * @example
     * Context.writeOutput("Processing completed successfully");
     */
    writeOutput: function(sMessage) {},

    /**
     * Writes log information
     * @param {string} sMessage - Log message
     * @example
     * Context.writeLog("Debug: Processing " + models.length + " models");
     */
    writeLog: function(sMessage) {},

    /**
     * Gets the script file path
     * @returns {string} Path to the current script
     * @example
     * var scriptPath = Context.getScriptFile();
     */
    getScriptFile: function() {},

    /**
     * Gets environment variable value
     * @param {string} sVarName - Environment variable name
     * @returns {string} Variable value
     * @example
     * var tempDir = Context.getEnvironmentVariable("TEMP");
     */
    getEnvironmentVariable: function(sVarName) {},

    /**
     * Gets parameter value passed to the script
     * @param {string} sParamName - Parameter name
     * @returns {string} Parameter value
     * @example
     * var userParam = Context.getParameter("UserName");
     */
    getParameter: function(sParamName) {},

    /**
     * Sets parameter value
     * @param {string} sParamName - Parameter name
     * @param {string} sValue - Parameter value
     * @example
     * Context.setParameter("OutputPath", "/home/<USER>/output/");
     */
    setParameter: function(sParamName, sValue) {},

    /**
     * Creates a folder at specified path
     * @param {string} sFolderPath - Folder path
     * @returns {boolean} True if successful
     * @example
     * var success = Context.createFolder("C:\\Reports\\Output");
     */
    createFolder: function(sFolderPath) {},

    /**
     * Checks if a file exists
     * @param {string} sFilePath - File path
     * @returns {boolean} True if file exists
     * @example
     * if (Context.fileExists("C:\\data\\input.xml")) {
     *     // Process file
     * }
     */
    fileExists: function(sFilePath) {},

    /**
     * Deletes a file
     * @param {string} sFilePath - File path
     * @returns {boolean} True if successful
     * @example
     * var deleted = Context.deleteFile("C:\\temp\\temp_file.tmp");
     */
    deleteFile: function(sFilePath) {},

    /**
     * Copies a file
     * @param {string} sSourcePath - Source file path
     * @param {string} sDestPath - Destination file path
     * @returns {boolean} True if successful
     * @example
     * var copied = Context.copyFile("source.txt", "backup.txt");
     */
    copyFile: function(sSourcePath, sDestPath) {},

    /**
     * Gets the current date and time
     * @returns {Date} Current date
     * @example
     * var now = Context.getDate();
     * var timestamp = now.getTime();
     */
    getDate: function() {},

    /**
     * Formats a date according to specified format
     * @param {Date} date - Date to format
     * @param {string} sFormat - Format string
     * @returns {string} Formatted date string
     * @example
     * var formatted = Context.formatDate(new Date(), "yyyy-MM-dd");
     */
    formatDate: function(date, sFormat) {},

    /**
     * Gets string from string table
     * Used for multilingual support
     * @param {string} sStringId - String identifier
     * @returns {string} Localized string
     * @example
     * var title = getString("ID_REPORT_TITLE");
     */
    getString: function(sStringId) {}
};

// Global function for string table access (commonly used)
/**
 * Gets localized string from string table
 * @param {string} sStringId - String identifier
 * @returns {string} Localized string value
 * @example
 * var headerText = getString("ID_HEADER_TEXT");
 */
function getString(sStringId) {}


// ============================================================================
// DIALOGS OBJECT
// ============================================================================

/**
 * Dialogs Object - Provides user interface dialogs for ARIS scripts
 * NOTE: Reports for Report Scheduler must not use dialogs - remove or comment out
 * @typedef {Object} DialogsObject
 * @global
 */
var Dialogs = {

    /**
     * Shows a message box dialog
     * @param {string} sMessage - Message text
     * @param {number} nType - Message box type (optional)
     * @param {string} sTitle - Dialog title (optional)
     * @returns {number} Button clicked by user
     * @example
     * Dialogs.MsgBox("Processing completed successfully!");
     * Dialogs.MsgBox("Are you sure?", Constants.MSGBOX_YESNO, "Confirmation");
     */
    MsgBox: function(sMessage, nType, sTitle) {},

    /**
     * Shows a file path selection dialog
     * @param {string} sTitle - Dialog title
     * @param {string} sFilter - File filter string
     * @param {string} sInitialPath - Initial path
     * @param {string} sDialogTitle - Dialog window title
     * @param {number} nFlags - Dialog flags
     * @returns {File[]} Array of selected files
     * @example
     * var files = Dialogs.getFilePath("", "*.xls|*.xls|Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*||", 
     *     "", "Select Excel file", 0);
     * if (files.length > 0) {
     *     var fileData = files[0].getData();
     * }
     */
    getFilePath: function(sTitle, sFilter, sInitialPath, sDialogTitle, nFlags) {},

    /**
     * Shows a folder selection dialog
     * @param {string} sTitle - Dialog title
     * @param {string} sInitialPath - Initial path
     * @returns {string} Selected folder path
     * @example
     * var folderPath = Dialogs.getFolderPath("Select output folder", "C:\\");
     */
    getFolderPath: function(sTitle, sInitialPath) {},

    /**
     * Shows an input dialog for text entry
     * @param {string} sTitle - Dialog title
     * @param {string} sPrompt - Prompt text
     * @param {string} sDefaultValue - Default input value
     * @returns {string} User input text
     * @example
     * var userName = Dialogs.InputBox("Enter your name", "User Name:", "");
     */
    InputBox: function(sTitle, sPrompt, sDefaultValue) {},

    /**
     * Shows a selection list dialog
     * @param {string[]} aItems - Array of items to choose from
     * @param {string} sTitle - Dialog title
     * @param {boolean} bMultiSelect - Allow multiple selection
     * @returns {number[]} Array of selected indices
     * @example
     * var modelTypes = ["EPC", "Org Chart", "Function Allocation"];
     * var selected = Dialogs.showDialog(modelTypes, "Select Model Types", true);
     */
    showDialog: function(aItems, sTitle, bMultiSelect) {}
};

// ============================================================================
// OUTPUT/REPORT CLASSES
// ============================================================================

/**
 * OutputObject class for creating formatted reports
 * @class
 */
function OutputObject() {}

/**
 * Begins a table in the output
 * @param {number} nWidth - Table width percentage
 * @param {number} nBorderColor - Border color
 * @param {number} nBgColor - Background color
 * @param {number} nFormat - Formatting flags
 * @param {number} nBorderWidth - Border width
 * @example
 * oOutput.BeginTable(100, Constants.C_BLACK, Constants.C_TRANSPARENT, 
 *     Constants.FMT_LEFT | Constants.FMT_REPEAT_HEADER, 0);
 */
OutputObject.prototype.BeginTable = function(nWidth, nBorderColor, nBgColor, nFormat, nBorderWidth) {};

/**
 * Ends the current table
 * @param {string} sName - Table name (optional)
 * @param {number} nWidth - Width percentage
 * @param {string} sFontName - Font name
 * @param {number} nFontSize - Font size
 * @param {number} nTextColor - Text color
 * @param {number} nBgColor - Background color
 * @param {number} nBorderWidth - Border width
 * @param {number} nFormat - Formatting flags
 * @param {number} nSpacing - Spacing
 * @example
 * oOutput.EndTable("", 100, "Arial", 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 0, 
 *     Constants.FMT_LEFT | Constants.FMT_VTOP, 0);
 */
OutputObject.prototype.EndTable = function(sName, nWidth, sFontName, nFontSize, nTextColor, nBgColor, nBorderWidth, nFormat, nSpacing) {};

/**
 * Creates a new table row
 * @example
 * oOutput.TableRow();
 */
OutputObject.prototype.TableRow = function() {};

/**
 * Creates a table cell with content
 * @param {string} sText - Cell text content
 * @param {number} nWidth - Cell width percentage
 * @param {string} sFontName - Font name
 * @param {number} nFontSize - Font size
 * @param {number} nTextColor - Text color
 * @param {number} nBgColor - Background color
 * @param {number} nBorderWidth - Border width
 * @param {number} nFormat - Formatting flags
 * @param {number} nSpacing - Cell spacing
 * @example
 * oOutput.TableCell("Function Name", 30, "Arial", 10, Constants.C_BLACK, 
 *     Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD | Constants.FMT_LEFT | Constants.FMT_VTOP, 0);
 */
OutputObject.prototype.TableCell = function(sText, nWidth, sFontName, nFontSize, nTextColor, nBgColor, nBorderWidth, nFormat, nSpacing) {};

/**
 * Outputs text with formatting
 * @param {string} sText - Text to output
 * @param {string} sFontName - Font name
 * @param {number} nFontSize - Font size
 * @param {number} nTextColor - Text color
 * @param {number} nBgColor - Background color
 * @param {number} nFormat - Formatting flags
 * @param {number} nSpacing - Text spacing
 * @example
 * oOutput.OutputLn("Report Title", "Arial", 14, Constants.C_BLACK, 
 *     Constants.C_TRANSPARENT, Constants.FMT_BOLD | Constants.FMT_CENTER, 0);
 */
OutputObject.prototype.OutputLn = function(sText, sFontName, nFontSize, nTextColor, nBgColor, nFormat, nSpacing) {};

/**
 * Outputs text without line break
 * @param {string} sText - Text to output
 * @param {string} sFontName - Font name
 * @param {number} nFontSize - Font size
 * @param {number} nTextColor - Text color
 * @param {number} nBgColor - Background color
 * @param {number} nFormat - Formatting flags
 * @param {number} nSpacing - Text spacing
 * @example
 * oOutput.Output("Label: ", "Arial", 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 
 *     Constants.FMT_BOLD, 0);
 */
OutputObject.prototype.Output = function(sText, sFontName, nFontSize, nTextColor, nBgColor, nFormat, nSpacing) {};

/**
 * Inserts a line break
 * @example
 * oOutput.OutputLnBreak();
 */
OutputObject.prototype.OutputLnBreak = function() {};

/**
 * Outputs a model graphic
 * @param {Model} model - Model to output
 * @param {boolean} bWithText - Include text annotations
 * @param {boolean} bWithFrame - Include model frame
 * @param {number} nFormat - Output format
 * @example
 * Context.setProperty("model-as-emf", true); // Better quality
 * oOutput.OutGraphic(model, true, false, Constants.FMT_LEFT);
 */
OutputObject.prototype.OutGraphic = function(model, bWithText, bWithFrame, nFormat) {};

/**
 * Sets the output file name
 * @param {string} sFileName - File name
 * @example
 * oOutput.DefineF(Context.getSelectedFile());
 */
OutputObject.prototype.DefineF = function(sFileName) {};

/**
 * Writes the report to output
 * @returns {boolean} True if successful
 * @example
 * if (oOutput.WriteReport()) {
 *     Context.writeOutput("Report generated successfully");
 * }
 */
OutputObject.prototype.WriteReport = function() {};

/**
 * Begins a list
 * @param {number} nFormat - List format
 * @example
 * oOutput.BeginList(Constants.FMT_LEFT);
 */
OutputObject.prototype.BeginList = function(nFormat) {};

/**
 * Ends the current list
 * @example
 * oOutput.EndList();
 */
OutputObject.prototype.EndList = function() {};

/**
 * Adds a list entry
 * @param {string} sText - Entry text
 * @param {string} sFontName - Font name
 * @param {number} nFontSize - Font size
 * @param {number} nTextColor - Text color
 * @param {number} nBgColor - Background color
 * @param {number} nFormat - Formatting flags
 * @param {number} nSpacing - Spacing
 * @example
 * oOutput.ListEntry("First item", "Arial", 10, Constants.C_BLACK, 
 *     Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);
 */
OutputObject.prototype.ListEntry = function(sText, sFontName, nFontSize, nTextColor, nBgColor, nFormat, nSpacing) {};

// ============================================================================
// FILTER CLASS
// ============================================================================

/**
 * Filter class representing an ARIS method filter
 * @class
 */
function Filter() {}

/**
 * Gets the type number for a user-defined attribute by GUID
 * IMPORTANT: Use this for custom attributes instead of hard-coded numbers
 * @param {string} sGuid - GUID of the user-defined attribute
 * @returns {number} Attribute type number
 * @example
 * var filter = ArisData.ActiveFilter();
 * var customAttrType = filter.UserDefinedAttributeTypeNum("12345678-1234-1234-1234-123456789abc");
 * var attrValue = objDef.Attribute(customAttrType, Context.getSelectedLanguage());
 */
Filter.prototype.UserDefinedAttributeTypeNum = function(sGuid) {};

/**
 * Gets the type number for a user-defined symbol by GUID
 * @param {string} sGuid - GUID of the user-defined symbol
 * @returns {number} Symbol type number
 * @example
 * var customSymbolType = filter.UserDefinedSymbolTypeNum("*************-4321-4321-cba987654321");
 * var objOccs = model.ObjOccListBySymbol([customSymbolType]);
 */
Filter.prototype.UserDefinedSymbolTypeNum = function(sGuid) {};

/**
 * Gets the type number for a user-defined model by GUID
 * @param {string} sGuid - GUID of the user-defined model
 * @returns {number} Model type number
 * @example
 * var customModelType = filter.UserDefinedModelTypeNum("abcdef12-3456-7890-abcd-ef1234567890");
 * var models = group.ModelList(true, [customModelType]);
 */
Filter.prototype.UserDefinedModelTypeNum = function(sGuid) {};

/**
 * Gets the type number for a user-defined connection by GUID
 * @param {string} sGuid - GUID of the user-defined connection
 * @returns {number} Connection type number
 * @example
 * var customCxnType = filter.UserDefinedConnectionTypeNum("fedcba09-**************-65432109fedc");
 */
Filter.prototype.UserDefinedConnectionTypeNum = function(sGuid) {};

// ============================================================================
// ATTRIBUTE CLASS
// ============================================================================

/**
 * Attribute class representing an object or model attribute
 * @class
 */
function Attribute() {}

/**
 * Gets the attribute value
 * @returns {string} Attribute value
 * @example
 * var nameAttr = objDef.Attribute(Constants.AT_NAME, Context.getSelectedLanguage());
 * var name = nameAttr.getValue();
 */
Attribute.prototype.getValue = function() {};

/**
 * Sets the attribute value
 * @param {string} sValue - New attribute value
 * @returns {boolean} True if successful
 * @example
 * var success = attr.setValue("New description text");
 */
Attribute.prototype.setValue = function(sValue) {};

/**
 * Gets the attribute type number
 * @returns {number} Attribute type number
 * @example
 * var attrType = attr.TypeNum();
 */
Attribute.prototype.TypeNum = function() {};

/**
 * Checks if the attribute is maintained (has a value)
 * @returns {boolean} True if attribute has a value
 * @example
 * if (attr.IsMaintained()) {
 *     var value = attr.getValue();
 * }
 */
Attribute.prototype.IsMaintained = function() {};


// ============================================================================
// COMPREHENSIVE USAGE EXAMPLES AND BEST PRACTICES
// ============================================================================

/**
 * ============================================================================
 * EXAMPLE 1: Basic Report Generation with Error Handling
 * ============================================================================
 * This example shows how to create a basic report with proper error handling
 */
function exampleBasicReport() {
    var oOutput;

    try {
        // Always use try/catch for error handling
        oOutput = Context.createOutputObject();
        var nLocale = Context.getSelectedLanguage();
        var selectedGroups = ArisData.getSelectedGroups();

        if (selectedGroups.length === 0) {
            Context.writeOutput("No groups selected!");
            return;
        }

        // Begin report
        oOutput.BeginTable(100, Constants.C_BLACK, Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);

        // Table header
        oOutput.TableRow();
        oOutput.TableCell("Group", 30, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
            Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD | Constants.FMT_CENTER, 0);
        oOutput.TableCell("Model", 40, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
            Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD | Constants.FMT_CENTER, 0);
        oOutput.TableCell("Type", 30, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
            Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD | Constants.FMT_CENTER, 0);

        // Process each group
        for (var i = 0; i < selectedGroups.length; i++) {
            var group = selectedGroups[i];
            var models = group.ModelList(true); // Get all models recursively

            for (var j = 0; j < models.length; j++) {
                var model = models[j];
                oOutput.TableRow();
                oOutput.TableCell(group.Name(nLocale), 30, getString("ID_DEFAULT_FONT"), 10, 
                    Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);
                oOutput.TableCell(model.Name(nLocale), 40, getString("ID_DEFAULT_FONT"), 10, 
                    Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);
                oOutput.TableCell(getModelTypeName(model.TypeNum()), 30, getString("ID_DEFAULT_FONT"), 10, 
                    Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

                // Release processed objects to save memory
                models[j] = null;
            }
            // Clear models array
            models = null;

            // Clear cache periodically when processing large amounts of data
            if (i % 10 === 9) {  // Every 10 groups
                group.Database().clearCaches();
            }
        }

        oOutput.EndTable("", 100, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
            Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

        // Write report
        oOutput.WriteReport();

    } catch (ex) {
        // Proper error handling with detailed information
        var line = ex.lineNumber;
        var message = ex.message;
        var filename = ex.fileName;
        var exJava = ex.javaException;

        if (exJava != null) {
            var stackTrace = exJava.getStackTrace();
            for (var iST = 0; iST < stackTrace.length; iST++) {
                message = message + "\n" + stackTrace[iST].toString();
            }
        }

        Dialogs.MsgBox("Exception in file " + filename + ", line " + line + ":\n" + message);
        Context.setScriptError(Constants.ERR_CANCEL);
    }
}

/**
 * ============================================================================
 * EXAMPLE 2: Performance-Optimized Object Processing
 * ============================================================================
 * This example demonstrates best practices for processing large datasets
 */
function exampleOptimizedProcessing() {
    try {
        var database = ArisData.getActiveDatabase();

        // Use SAVE_ONDEMAND for batch operations - more efficient
        ArisData.Save(Constants.SAVE_ONDEMAND);

        // Get selected groups
        var selectedGroups = ArisData.getSelectedGroups();

        for (var i = 0; i < selectedGroups.length; i++) {
            var group = selectedGroups[i];

            // BEST PRACTICE: Use type filtering for better performance
            // Instead of: var allModels = group.ModelList(true);
            var epcModels = group.ModelList(true, [Constants.MT_EEPC, Constants.MT_EEPC_COLUMN]);

            for (var j = 0; j < epcModels.length; j++) {
                var model = epcModels[j];

                // BEST PRACTICE: Use optimized methods
                // Instead of: var allOccs = model.ObjOccList(); then filter manually
                var functionOccs = model.ObjOccListBySymbol([Constants.ST_FUNC]);

                // Process function occurrences
                for (var k = 0; k < functionOccs.length; k++) {
                    var objOcc = functionOccs[k];
                    var objDef = objOcc.ObjDef();

                    // Process the object definition
                    processObjectDefinition(objDef);

                    // Release processed objects
                    functionOccs[k] = null;
                }

                // Clear processed arrays
                functionOccs = null;
                epcModels[j] = null;
            }

            epcModels = null;

            // Clear caches periodically for large datasets
            database.clearCaches();
        }

        // Save all changes at once
        ArisData.Save(Constants.SAVE_NOW);

        // Reset to auto save mode
        ArisData.Save(Constants.SAVE_AUTO);

    } catch (ex) {
        // Clean up on error
        ArisData.Save(Constants.SAVE_NOW);
        ArisData.Save(Constants.SAVE_AUTO);
        handleError(ex);
    }
}

/**
 * ============================================================================
 * EXAMPLE 3: Working with Custom Attributes and User-Defined Types
 * ============================================================================
 */
function exampleCustomAttributes() {
    try {
        var filter = ArisData.ActiveFilter();

        // Get type numbers for custom attributes using GUIDs (best practice)
        var customStatusAttr = filter.UserDefinedAttributeTypeNum("12345678-1234-1234-1234-123456789abc");
        var customPriorityAttr = filter.UserDefinedAttributeTypeNum("*************-4321-4321-cba987654321");

        // Get custom symbol type
        var customSymbolType = filter.UserDefinedSymbolTypeNum("abcdef12-3456-7890-abcd-ef1234567890");

        var selectedModels = ArisData.getSelectedModels();
        var nLocale = Context.getSelectedLanguage();

        for (var i = 0; i < selectedModels.length; i++) {
            var model = selectedModels[i];

            // Get objects with custom symbol type
            var customObjects = model.ObjOccListBySymbol([customSymbolType]);

            for (var j = 0; j < customObjects.length; j++) {
                var objOcc = customObjects[j];
                var objDef = objOcc.ObjDef();

                // Read custom attributes
                var statusAttr = objDef.Attribute(customStatusAttr, nLocale);
                var priorityAttr = objDef.Attribute(customPriorityAttr, nLocale);

                if (statusAttr.IsMaintained()) {
                    var status = statusAttr.getValue();
                    Context.writeLog("Object: " + objDef.Name(nLocale) + ", Status: " + status);
                }

                // Set custom attribute value
                if (priorityAttr.IsMaintained()) {
                    objDef.setAttribute(customPriorityAttr, nLocale, "High");
                }

                customObjects[j] = null;
            }

            customObjects = null;
            selectedModels[i] = null;
        }

    } catch (ex) {
        handleError(ex);
    }
}

/**
 * ============================================================================
 * EXAMPLE 4: Model Graphics and Advanced Output Formatting
 * ============================================================================
 */
function exampleAdvancedOutput() {
    try {
        var oOutput = Context.createOutputObject();

        // Set property for better model graphics quality in PDF
        Context.setProperty("model-as-emf", true);

        var selectedModels = ArisData.getSelectedModels();
        var nLocale = Context.getSelectedLanguage();

        // Output header
        oOutput.OutputLn(getString("ID_REPORT_TITLE"), getString("ID_DEFAULT_FONT"), 16, 
            Constants.C_BLACK, Constants.C_TRANSPARENT, Constants.FMT_BOLD | Constants.FMT_CENTER, 0);
        oOutput.OutputLnBreak();

        for (var i = 0; i < selectedModels.length; i++) {
            var model = selectedModels[i];

            // Model information table
            oOutput.BeginTable(100, Constants.C_BLACK, Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);

            oOutput.TableRow();
            oOutput.TableCell(getString("ID_MODEL_NAME"), 20, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD, 0);
            oOutput.TableCell(model.Name(nLocale), 80, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

            oOutput.TableRow();
            oOutput.TableCell(getString("ID_MODEL_TYPE"), 20, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_BOLD, 0);
            oOutput.TableCell(getModelTypeName(model.TypeNum()), 80, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

            oOutput.EndTable("", 100, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
                Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

            // Output model graphic
            oOutput.OutGraphic(model, true, false, Constants.FMT_CENTER);
            oOutput.OutputLnBreak();

            // Object statistics
            var objOccs = model.ObjOccList();
            var functionCount = 0;
            var eventCount = 0;

            for (var j = 0; j < objOccs.length; j++) {
                var objOcc = objOccs[j];
                var objType = objOcc.ObjDef().TypeNum();

                if (objType === Constants.OT_FUNC) {
                    functionCount++;
                } else if (objType === Constants.OT_EVT) {
                    eventCount++;
                }

                objOccs[j] = null;
            }

            // Statistics table
            oOutput.BeginTable(50, Constants.C_BLACK, Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);
            oOutput.TableRow();
            oOutput.TableCell(getString("ID_FUNCTIONS"), 50, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);
            oOutput.TableCell(functionCount.toString(), 50, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_RIGHT, 0);

            oOutput.TableRow();
            oOutput.TableCell(getString("ID_EVENTS"), 50, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);
            oOutput.TableCell(eventCount.toString(), 50, getString("ID_DEFAULT_FONT"), 10, 
                Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_RIGHT, 0);

            oOutput.EndTable("", 50, getString("ID_DEFAULT_FONT"), 10, Constants.C_BLACK, 
                Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);

            objOccs = null;
            selectedModels[i] = null;
        }

        oOutput.WriteReport();

    } catch (ex) {
        handleError(ex);
    }
}

/**
 * ============================================================================
 * UTILITY FUNCTIONS
 * ============================================================================
 */

/**
 * Helper function to get model type name
 */
function getModelTypeName(nModelType) {
    switch (nModelType) {
        case Constants.MT_EEPC: return "EPC";
        case Constants.MT_ORG_CHRT: return "Organization Chart";
        case Constants.MT_FUNC_ALLOC_DGM: return "Function Allocation Diagram";
        default: return "Unknown Model Type";
    }
}

/**
 * Helper function to process an object definition
 */
function processObjectDefinition(objDef) {
    var nLocale = Context.getSelectedLanguage();
    var name = objDef.Name(nLocale);
    var type = objDef.TypeNum();

    // Process based on object type
    if (type === Constants.OT_FUNC) {
        processFunctionObject(objDef);
    } else if (type === Constants.OT_EVT) {
        processEventObject(objDef);
    }
}

/**
 * Helper function for consistent error handling
 */
function handleError(ex) {
    var errorMessage = "Error: " + ex.message;
    if (ex.lineNumber) {
        errorMessage += " (Line: " + ex.lineNumber + ")";
    }

    Context.writeLog(errorMessage);
    Dialogs.MsgBox(errorMessage, Constants.MSGBOX_ICON_ERROR, "Script Error");
    Context.setScriptError(Constants.ERR_CANCEL);
}

/**
 * ============================================================================
 * BEST PRACTICES SUMMARY
 * ============================================================================
 * 
 * 1. MEMORY MANAGEMENT:
 *    - Always declare variables with 'var'
 *    - Set unused variables to null to release memory
 *    - Use database.clearCaches() for large datasets
 *    - Avoid global variables for data processing
 * 
 * 2. PERFORMANCE OPTIMIZATION:
 *    - Use filtered methods: ModelList(recursive, types), ObjOccListBySymbol()
 *    - Use ArisData.Unique() instead of custom duplicate removal
 *    - Use SAVE_ONDEMAND for batch operations, then SAVE_NOW
 *    - Process data in functional blocks, not one large function
 * 
 * 3. ERROR HANDLING:
 *    - Always use try/catch blocks
 *    - Provide detailed error information
 *    - Clean up resources in catch blocks
 *    - Use Context.setScriptError() to cancel execution
 * 
 * 4. CUSTOM TYPES:
 *    - Use GUIDs with UserDefinedAttributeTypeNum() for custom attributes
 *    - Never hard-code type numbers for user-defined types
 *    - Store type numbers in variables at script start
 * 
 * 5. REPORTS:
 *    - Use getString() for multilingual support
 *    - Set Context.setProperty("model-as-emf", true) for better PDF graphics
 *    - Remove dialogs for Report Scheduler compatibility
 *    - Validate input data before processing
 * 
 * 6. CONNECTIONS AND ATTRIBUTES:
 *    - Reading one attribute/connection loads ALL into memory
 *    - Use filtered methods when possible
 *    - Release connection/attribute arrays after processing
 * 
 * 7. MODEL CREATION:
 *    - Use appropriate symbol types for object occurrences
 *    - Set object positions and connections properly
 *    - Save models in batches for better performance
 */
