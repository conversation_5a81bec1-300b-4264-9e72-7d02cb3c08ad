# ARIS Script API Reference - Usage Instructions

## Overview
This file provides complete IntelliSense support for ARIS Script development in VSCode.

## Setup Instructions

### 1. Add to VSCode Workspace
1. Copy `aris_script_api_complete_reference.js` to your VSCode workspace
2. Place it in a folder like `references/` or `intellisense/`
3. VSCode will automatically recognize the JSDoc definitions

### 2. Enable IntelliSense
- The file contains comprehensive JSDoc comments
- All ARIS objects, methods, and constants are documented
- Parameter types and return values are specified
- Usage examples are included for complex methods

### 3. Using the Reference
- Start typing any ARIS object name (ArisData, Context, etc.)
- VSCode will show autocomplete suggestions
- Hover over methods to see documentation
- Parameter hints will appear as you type

## File Contents

### Core Objects
- **ArisData**: Main entry point with selection and utility methods
- **Database**: Database operations and searching
- **Group**: Group/folder operations and content listing
- **Model**: Model operations and object management
- **ObjDef/ObjOcc**: Object definition and occurrence methods
- **Context**: Environment and configuration methods
- **Dialogs**: User interface dialogs
- **Constants**: All ARIS constant values

### Utility Classes
- **OutputObject**: Report generation and formatting
- **Filter**: Method filter operations
- **Attribute**: Attribute value operations
- **CxnDef/CxnOcc**: Connection operations

### Best Practices Included
- Memory management techniques
- Performance optimization patterns
- Error handling examples
- Custom attribute handling
- Batch processing methods

## Examples Included
1. Basic report generation with error handling
2. Performance-optimized object processing
3. Custom attributes and user-defined types
4. Advanced output formatting and graphics

## Performance Tips
- Use filtered methods (ModelList with types, ObjOccListBySymbol)
- Implement proper memory management (set variables to null)
- Use SAVE_ONDEMAND for batch operations
- Clear caches periodically for large datasets

## Error Handling
- Always use try/catch blocks
- Implement proper cleanup in catch blocks
- Use Context.setScriptError() for controlled exits
- Provide detailed error messages

## Custom Types
- Use GUIDs with UserDefinedAttributeTypeNum() for custom attributes
- Never hard-code type numbers for user-defined types
- Store type numbers in variables at script start

Enjoy enhanced ARIS script development with full IntelliSense support!
