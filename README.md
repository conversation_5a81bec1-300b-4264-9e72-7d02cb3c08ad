# ARIS Script API Reference - Usage Instructions

## Overview
This file provides complete IntelliSense support for ARIS Script development in VSCode.

## Setup Instructions

### Method 1: Complete Workspace Setup (Recommended)
This workspace is now configured with full TypeScript IntelliSense support:

**Files Created:**
- `jsconfig.json` - JavaScript project configuration
- `types/aris-script-api.d.ts` - TypeScript definitions
- `.vscode/settings.json` - VS Code settings for optimal IntelliSense
- `sample-aris-script.js` - Example script to test functionality

**Features:**
- ✅ Full autocomplete for all ARIS objects (ArisData, Context, Dialogs, Constants)
- ✅ Method parameter hints and documentation on hover
- ✅ Constant values autocomplete (Constants.SEARCH_MODEL, etc.)
- ✅ Type checking and validation

**Usage:**
1. Open this folder as a workspace in VS Code
2. Create new `.js` files for your ARIS scripts
3. Start typing ARIS API calls and enjoy full IntelliSense!

### Method 2: Simple Reference File
1. Copy `aris_script_api_complete_reference.js` to your VSCode workspace
2. Place it in a folder like `references/` or `intellisense/`
3. VSCode will automatically recognize the JSDoc definitions

### Testing the Setup
Open `sample-aris-script.js` and try:
- Type `ArisData.` - you should see all available methods
- Type `Constants.` - you should see all ARIS constants
- Hover over any method to see documentation
- Use Ctrl+Space for autocomplete suggestions

## File Contents

### Core Objects
- **ArisData**: Main entry point with selection and utility methods
- **Database**: Database operations and searching
- **Group**: Group/folder operations and content listing
- **Model**: Model operations and object management
- **ObjDef/ObjOcc**: Object definition and occurrence methods
- **Context**: Environment and configuration methods
- **Dialogs**: User interface dialogs
- **Constants**: All ARIS constant values

### Utility Classes
- **OutputObject**: Report generation and formatting
- **Filter**: Method filter operations
- **Attribute**: Attribute value operations
- **CxnDef/CxnOcc**: Connection operations

### Best Practices Included
- Memory management techniques
- Performance optimization patterns
- Error handling examples
- Custom attribute handling
- Batch processing methods

## Examples Included
1. Basic report generation with error handling
2. Performance-optimized object processing
3. Custom attributes and user-defined types
4. Advanced output formatting and graphics

## Performance Tips
- Use filtered methods (ModelList with types, ObjOccListBySymbol)
- Implement proper memory management (set variables to null)
- Use SAVE_ONDEMAND for batch operations
- Clear caches periodically for large datasets

## Error Handling
- Always use try/catch blocks
- Implement proper cleanup in catch blocks
- Use Context.setScriptError() for controlled exits
- Provide detailed error messages

## Custom Types
- Use GUIDs with UserDefinedAttributeTypeNum() for custom attributes
- Never hard-code type numbers for user-defined types
- Store type numbers in variables at script start

## Troubleshooting

If IntelliSense isn't working:
1. **Restart VS Code** - Sometimes the TypeScript language service needs a restart
2. **Restart TS Server** - Run "TypeScript: Restart TS Server" from Command Palette (Ctrl+Shift+P)
3. **Check JavaScript Language Service** - Ensure it's enabled in VS Code settings
4. **Verify jsconfig.json** - Make sure the file is in the workspace root
5. **Check file associations** - Ensure `.js` files are associated with JavaScript

## Additional Tips
- Use the workspace folder as your project root for best results
- The setup works with both individual `.js` files and larger ARIS script projects
- All ARIS constants, objects, and methods from the documentation are included

Enjoy enhanced ARIS script development with full IntelliSense support!
