/**
 * Sample ARIS Script to test IntelliSense
 * This file demonstrates how the ARIS API definitions work with VS Code
 */

// Test global objects - you should see autocomplete when typing these
var database = ArisData.getActiveDatabase();
var selectedModels = ArisData.getSelectedModels();

// Test Constants - you should see all ARIS constants when typing Constants.
var searchType = Constants.SEARCH_MODEL;
var objectType = Constants.OT_FUNC;

// Test Context object
var locale = Context.getSelectedLanguage();
var outputDir = Context.getSelectedPath();

// Test method calls with parameters - hover over methods to see documentation
var models = database.Find(Constants.SEARCH_MODEL);
var groups = database.RootGroup().Childs();

// Example function using ARIS API
function processSelectedModels() {
    var selectedModels = ArisData.getSelectedModels();
    
    if (selectedModels.length === 0) {
        Dialogs.MsgBox("No models selected", Constants.MSGBOX_BTN_OK, "Information");
        return;
    }
    
    for (var i = 0; i < selectedModels.length; i++) {
        var model = selectedModels[i];
        var modelName = model.Name(Context.getSelectedLanguage());
        
        // Process each model
        Context.writeOutput("Processing model: " + modelName);
    }
}

// Call the function
processSelectedModels();
