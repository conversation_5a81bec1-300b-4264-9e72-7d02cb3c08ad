/**
 * ARIS Script API Type Definitions
 * This file provides TypeScript definitions for ARIS scripting IntelliSense
 */

/// <reference path="../aris_script_api_complete_reference.js" />

declare global {
    /**
     * ARIS Data Object - provides utility functions for ARIS data manipulation
     */
    var ArisData: ArisDataObject;
    
    /**
     * Context Object - provides access to the current ARIS context
     */
    var Context: ContextObject;
    
    /**
     * Dialogs Object - provides dialog functionality
     */
    var Dialogs: DialogsObject;
    
    /**
     * Constants Object - contains all ARIS script constants
     */
    var Constants: ConstantsObject;
}

// Re-export the types from the main reference file
export {};
